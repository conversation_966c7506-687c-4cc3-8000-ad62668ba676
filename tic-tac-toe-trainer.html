<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> Training Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            color: white;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .setup-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            align-items: center;
        }

        .setup-group {
            text-align: center;
        }

        .setup-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 1.1em;
        }

        select, button {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            width: 100%;
        }

        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-weight: bold;
            width: 100%;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .game-area {
            display: none;
            text-align: center;
        }

        .game-area.active {
            display: block;
        }

        .status {
            margin-bottom: 20px;
            font-size: 1.3em;
            font-weight: bold;
            min-height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
        }

        .board {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            max-width: 400px;
            margin: 0 auto 30px;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 15px;
        }

        .cell {
            aspect-ratio: 1;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 8px;
            font-size: 3em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .cell:hover:not(:disabled) {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }

        .cell:disabled {
            cursor: not-allowed;
        }

        .cell.x {
            color: #e74c3c;
        }

        .cell.o {
            color: #3498db;
        }

        .recommended-move {
            background: rgba(46, 204, 113, 0.3) !important;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .controls button {
            width: auto;
            min-width: 120px;
        }

        .mode-indicator {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .setup-panel {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .cell {
                font-size: 2em;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Tic-Tac-Toe Training Assistant</h1>
        
        <div class="setup-panel" id="setupPanel">
            <div class="setup-group">
                <label for="playerSymbol">Your Symbol:</label>
                <select id="playerSymbol">
                    <option value="X">X</option>
                    <option value="O">O</option>
                </select>
            </div>
            
            <div class="setup-group">
                <label for="firstPlayer">Who Goes First:</label>
                <select id="firstPlayer">
                    <option value="player">You</option>
                    <option value="opponent">Opponent</option>
                </select>
            </div>
            
            <div class="setup-group">
                <label>&nbsp;</label>
                <button onclick="startGame()">Start Training</button>
            </div>
        </div>

        <div class="game-area" id="gameArea">
            <div class="mode-indicator" id="modeIndicator"></div>
            <div class="status" id="status"></div>
            
            <div class="board" id="board">
                <!-- Cells will be generated by JavaScript -->
            </div>
            
            <div class="controls">
                <button onclick="resetGame()">New Game</button>
                <button onclick="showSetup()">Change Settings</button>
                <button onclick="getHint()" id="hintButton">Get Hint</button>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let board = Array(9).fill('');
        let playerSymbol = 'X';
        let opponentSymbol = 'O';
        let currentPlayer = 'X';
        let gameActive = false;
        let playerGoesFirst = true;
        let gameMode = 'training'; // 'training' mode for assistance

        // Initialize the game
        function initializeBoard() {
            const boardElement = document.getElementById('board');
            boardElement.innerHTML = '';
            
            for (let i = 0; i < 9; i++) {
                const cell = document.createElement('button');
                cell.className = 'cell';
                cell.onclick = () => handleCellClick(i);
                cell.id = `cell-${i}`;
                boardElement.appendChild(cell);
            }
        }

        function startGame() {
            // Get settings
            playerSymbol = document.getElementById('playerSymbol').value;
            opponentSymbol = playerSymbol === 'X' ? 'O' : 'X';
            playerGoesFirst = document.getElementById('firstPlayer').value === 'player';
            
            // Reset game state
            board = Array(9).fill('');
            currentPlayer = 'X';
            gameActive = true;
            
            // Show game area
            document.getElementById('setupPanel').style.display = 'none';
            document.getElementById('gameArea').classList.add('active');
            
            // Initialize board
            initializeBoard();
            updateDisplay();
            
            // Set initial turn
            if (playerGoesFirst) {
                if (playerSymbol === 'X') {
                    showPlayerTurn();
                } else {
                    currentPlayer = 'O';
                    showPlayerTurn();
                }
            } else {
                if (opponentSymbol === 'X') {
                    currentPlayer = 'X';
                    showOpponentTurn();
                } else {
                    currentPlayer = 'O';
                    showOpponentTurn();
                }
            }
        }

        function showSetup() {
            document.getElementById('setupPanel').style.display = 'grid';
            document.getElementById('gameArea').classList.remove('active');
        }

        function resetGame() {
            startGame();
        }

        function handleCellClick(index) {
            if (!gameActive || board[index] !== '') return;
            
            if (currentPlayer === playerSymbol) {
                // Player's move
                makeMove(index, playerSymbol);
                if (gameActive) {
                    currentPlayer = opponentSymbol;
                    showOpponentTurn();
                }
            } else {
                // Input opponent's move
                makeMove(index, opponentSymbol);
                if (gameActive) {
                    currentPlayer = playerSymbol;
                    showPlayerTurn();
                }
            }
        }

        function makeMove(index, symbol) {
            board[index] = symbol;
            updateDisplay();

            if (checkWinner()) {
                gameActive = false;
                const winner = checkWinner();
                if (winner === 'tie') {
                    updateStatus("Game Over - It's a tie!");
                } else if (winner === playerSymbol) {
                    updateStatus("🎉 You won! Great job!");
                } else {
                    updateStatus("😞 You lost. Try the recommended moves next time!");
                }
                clearRecommendations();
                return;
            }
        }

        function showPlayerTurn() {
            clearRecommendations();
            const bestMove = getBestMove(board, playerSymbol);

            if (bestMove !== -1) {
                document.getElementById(`cell-${bestMove}`).classList.add('recommended-move');
                updateStatus(`🎯 Your turn! Recommended move: Position ${bestMove + 1} (highlighted in green)`);
            } else {
                updateStatus("🎯 Your turn! Make your move.");
            }

            updateModeIndicator("Your Turn - AI suggests the optimal move");
        }

        function showOpponentTurn() {
            clearRecommendations();
            updateStatus("⏳ Input your opponent's move by clicking on the board");
            updateModeIndicator("Opponent's Turn - Click where your opponent played");
        }

        function updateDisplay() {
            for (let i = 0; i < 9; i++) {
                const cell = document.getElementById(`cell-${i}`);
                cell.textContent = board[i];
                cell.className = 'cell';
                if (board[i]) {
                    cell.classList.add(board[i].toLowerCase());
                    cell.disabled = true;
                } else {
                    cell.disabled = false;
                }
            }
        }

        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
        }

        function updateModeIndicator(message) {
            document.getElementById('modeIndicator').textContent = message;
        }

        function clearRecommendations() {
            document.querySelectorAll('.cell').forEach(cell => {
                cell.classList.remove('recommended-move');
            });
        }

        function getHint() {
            if (!gameActive || currentPlayer !== playerSymbol) return;

            clearRecommendations();
            const bestMove = getBestMove(board, playerSymbol);

            if (bestMove !== -1) {
                document.getElementById(`cell-${bestMove}`).classList.add('recommended-move');
                updateStatus(`💡 Hint: Best move is position ${bestMove + 1} (highlighted in green)`);
            }
        }

        // Minimax Algorithm Implementation
        function getBestMove(currentBoard, symbol) {
            let bestScore = -Infinity;
            let bestMove = -1;

            for (let i = 0; i < 9; i++) {
                if (currentBoard[i] === '') {
                    currentBoard[i] = symbol;
                    let score = minimax(currentBoard, 0, false, symbol);
                    currentBoard[i] = '';

                    if (score > bestScore) {
                        bestScore = score;
                        bestMove = i;
                    }
                }
            }

            return bestMove;
        }

        function minimax(currentBoard, depth, isMaximizing, playerSym) {
            const winner = checkWinner(currentBoard);

            if (winner === playerSym) return 10 - depth;
            if (winner === (playerSym === 'X' ? 'O' : 'X')) return depth - 10;
            if (winner === 'tie') return 0;

            if (isMaximizing) {
                let bestScore = -Infinity;
                for (let i = 0; i < 9; i++) {
                    if (currentBoard[i] === '') {
                        currentBoard[i] = playerSym;
                        let score = minimax(currentBoard, depth + 1, false, playerSym);
                        currentBoard[i] = '';
                        bestScore = Math.max(score, bestScore);
                    }
                }
                return bestScore;
            } else {
                let bestScore = Infinity;
                const opponent = playerSym === 'X' ? 'O' : 'X';
                for (let i = 0; i < 9; i++) {
                    if (currentBoard[i] === '') {
                        currentBoard[i] = opponent;
                        let score = minimax(currentBoard, depth + 1, true, playerSym);
                        currentBoard[i] = '';
                        bestScore = Math.min(score, bestScore);
                    }
                }
                return bestScore;
            }
        }

        function checkWinner(boardToCheck = board) {
            const winPatterns = [
                [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
                [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
                [0, 4, 8], [2, 4, 6] // Diagonals
            ];

            for (let pattern of winPatterns) {
                const [a, b, c] = pattern;
                if (boardToCheck[a] && boardToCheck[a] === boardToCheck[b] && boardToCheck[a] === boardToCheck[c]) {
                    return boardToCheck[a];
                }
            }

            if (boardToCheck.every(cell => cell !== '')) {
                return 'tie';
            }

            return null;
        }

        // Initialize the game when page loads
        window.onload = function() {
            initializeBoard();
        };
    </script>
</body>
</html>
